package com.enosisbd.api.server.exception;

import com.enosisbd.api.server.model.RestResponse;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.io.IOException;
import java.security.GeneralSecurityException;

/**
 * Exception handler for Google Sheets API errors
 */
@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
@Log4j2
public class GoogleSheetsExceptionHandler {
    
    /**
     * Handle Google JSON response exceptions
     */
    @ExceptionHandler(GoogleJsonResponseException.class)
    public ResponseEntity<RestResponse<String>> handleGoogleJsonResponseException(GoogleJsonResponseException ex) {
        log.error("Google Sheets API error: {}", ex.getMessage(), ex);
        
        HttpStatus status;
        String message;
        
        // Map Google API error codes to appropriate HTTP status codes
        switch (ex.getStatusCode()) {
            case 400:
                status = HttpStatus.BAD_REQUEST;
                message = "Invalid request to Google Sheets API";
                break;
            case 401:
                status = HttpStatus.UNAUTHORIZED;
                message = "Unauthorized access to Google Sheets API. Check your credentials.";
                break;
            case 403:
                status = HttpStatus.FORBIDDEN;
                message = "Access forbidden. Make sure the Google Sheet is shared with the service account.";
                break;
            case 404:
                status = HttpStatus.NOT_FOUND;
                message = "Google Sheet not found. Check the URL and make sure it's accessible.";
                break;
            default:
                status = HttpStatus.INTERNAL_SERVER_ERROR;
                message = "Error accessing Google Sheets API: " + ex.getMessage();
        }
        
        return ResponseEntity.status(status)
                .body(RestResponse.error(message));
    }
    
    /**
     * Handle general IO exceptions
     */
    @ExceptionHandler(IOException.class)
    public ResponseEntity<RestResponse<String>> handleIOException(IOException ex) {
        log.error("IO error accessing Google Sheets: {}", ex.getMessage(), ex);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(RestResponse.error("Error accessing Google Sheets: " + ex.getMessage()));
    }
    
    /**
     * Handle security exceptions
     */
    @ExceptionHandler(GeneralSecurityException.class)
    public ResponseEntity<RestResponse<String>> handleGeneralSecurityException(GeneralSecurityException ex) {
        log.error("Security error accessing Google Sheets: {}", ex.getMessage(), ex);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(RestResponse.error("Security error accessing Google Sheets. Check your credentials."));
    }
}
