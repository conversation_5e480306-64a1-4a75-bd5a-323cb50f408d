package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SubModuleDto extends BaseDto {
    @NotNull(message = "SubModule name cannot be null")
    @Size(min = 3, max = 255, message = "SubModule name must be between 3 and 255 characters")
    private String name;

    @NotNull(message = "Module ID cannot be null")
    private Long moduleId;

    private Integer startRow;
    private Integer endRow;
}
