package com.enosisbd.api.server.service.tree;

import com.enosisbd.api.server.dto.TreeNodeDto;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for building project tree structures
 */
public interface ProjectTreeService {
    
    /**
     * Get a tree structure for a project including its modules and submodules
     * 
     * @param projectId The ID of the project
     * @return A tree structure representing the project hierarchy
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    TreeNodeDto getProjectTree(Long projectId);
}
