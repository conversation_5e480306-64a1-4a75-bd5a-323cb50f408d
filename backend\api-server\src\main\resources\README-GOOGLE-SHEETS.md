# Google Sheets API Integration

This document provides instructions for setting up the Google Sheets API integration for the Enosis Portal application.

## Overview

The application uses a **hybrid approach** for accessing Google Sheets based on user authentication method:

### **For Google OAuth Users:**
- **User OAuth Access**: Uses individual user's Google OAuth tokens to access sheets they have permission to view
- **Works for both private and public sheets**: User's OAuth tokens can access any sheet they have permission to view

### **For Local Users (Email/Password):**
- **API Key Access**: Uses Google API key to access publicly available Google Sheets only
- **Limited to public sheets**: Cannot access private sheets unless they are publicly shared

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A GCP project with the Google Sheets API and Google Drive API enabled
3. OAuth 2.0 credentials configured for user authentication
4. Google API key for public sheet access (for local users)

## Setup Instructions

### 1. Create a Google Cloud Platform Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Make note of your project ID

### 2. Enable Required APIs

1. In the Google Cloud Console, navigate to "APIs & Services" > "Library"
2. Search for and enable the following APIs:
   - **Google Sheets API** (required)

### 3. Set up OAuth 2.0 Credentials (Primary Method)

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" and select "OAuth 2.0 Client IDs"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in the required application information
   - Add your domain to authorized domains
   - Add the following scopes:
     - `email`
     - `profile`
     - `https://www.googleapis.com/auth/spreadsheets.readonly`
4. For the OAuth 2.0 Client ID:
   - Application type: "Web application"
   - Name: "Enosis Portal"
   - Authorized redirect URIs: Add your callback URL (e.g., `http://localhost:8085/api/auth/oauth2/google/callback`)
5. Download the client configuration and note the Client ID and Client Secret

### 4. Create Google API Key (For Local Users)

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" and select "API key"
3. Copy the generated API key
4. (Recommended) Click "Restrict key" to limit its usage:
   - **Application restrictions**: Set to "None" or configure for your server IPs
   - **API restrictions**: Select "Restrict key" and choose:
     - Google Sheets API
5. Save the restrictions

### 5. Configure the Application

#### Required Environment Variables

Set the following environment variables:

```bash
# Google OAuth Configuration (Required for Google OAuth users)
app.google-oauth2.client-id=your_oauth_client_id
app.google-oauth2.client-secret=your_oauth_client_secret
app.google-oauth2.url=http://localhost:8085  # Your backend URL

# Frontend URL for OAuth redirects
app.frontend.url=http://localhost:3000  # Your frontend URL

# Google API Key (Required for local users to access public sheets)
app.google.api.key=your_google_api_key

# OAuth Token Encryption (Required for security)
app.security.token.encryption.key=your_encryption_key
```

#### Application Configuration

The application will automatically:

1. **For Google OAuth Users**: Use the user's OAuth tokens to access both private and public sheets
2. **For Local Users**: Use Google API key to access public sheets only
3. **Fallback**: Provide clear error messages when access is not possible

## How It Works

### Authentication Flow

1. **User Login**: Users authenticate with Google OAuth, granting the application permission to access their Google Sheets
2. **Token Storage**: The application securely stores the user's Google access and refresh tokens in the database
3. **Sheet Access**: When accessing a Google Sheet, the application uses the user's stored tokens
4. **Token Refresh**: Expired tokens are automatically refreshed using the stored refresh token

### Access Modes

#### 1. Google OAuth User Access (Recommended)

- **How it works**: Uses the logged-in user's Google OAuth tokens
- **Advantages**:
  - Access to both private and public sheets
  - No need to manually share sheets
  - Respects user's existing Google permissions
  - More secure and user-friendly
- **Requirements**: User must be logged in via Google OAuth

#### 2. Local User Access (API Key)

- **How it works**: Uses Google API key for public sheet access
- **Advantages**:
  - Works for users who prefer email/password login
  - No OAuth setup required for users
- **Limitations**:
  - Only works with publicly accessible sheets
  - Cannot access private sheets
- **Requirements**: Google API key must be configured

### Setting Up Sheet Access

#### For User OAuth Access (Recommended)

1. Users simply log in with their Google account
2. The application automatically gains access to sheets the user can view
3. No manual sharing required

## Troubleshooting

### Common Issues

#### 403 Forbidden Error
- **User OAuth**: User doesn't have access to the sheet or hasn't logged in via Google OAuth
- **Service Account**: Sheet is not shared with the service account email
- **Solution**: Ensure user has access or share sheet with service account

#### 404 Not Found Error
- **Cause**: Invalid Google Sheet URL or Sheet ID
- **Solution**: Verify the Google Sheet URL is correct and accessible

#### 401 Unauthorized Error
- **Cause**: Expired or invalid OAuth tokens
- **Solution**: User should log out and log back in via Google OAuth

#### 500 Internal Server Error
- **Cause**: Various configuration or server issues
- **Solution**: Check application logs for detailed error messages

### Debugging Steps

1. **Check OAuth Configuration**:
   - Verify `app.google-oauth2.client-id` and `app.google-oauth2.client-secret` are set correctly
   - Ensure redirect URI in Google Cloud Console matches your application URL

2. **Verify API Enablement**:
   - Confirm Google Sheets API and Google Drive API are enabled in Google Cloud Console

3. **Check User Authentication**:
   - Ensure user has logged in via Google OAuth
   - Check if user's tokens are stored in the database

4. **Service Account Fallback**:
   - If using service account fallback, ensure `credentials.json` is in the correct location
   - Verify service account has necessary permissions

5. **Application Logs**:
   - Check logs for detailed error messages
   - Look for token refresh attempts and their results

### Testing the Integration

1. **Test OAuth Login**: Verify users can log in via Google OAuth
2. **Test Sheet Access**: Try accessing a sheet the user owns
3. **Test Token Refresh**: Wait for token expiry and verify automatic refresh
4. **Test Fallback**: Try accessing with service account when user tokens are unavailable

### Environment Variables Checklist

Ensure these environment variables are properly set:

```bash
# Required for OAuth
app.google-oauth2.client-id=your_oauth_client_id
app.google-oauth2.client-secret=your_oauth_client_secret
app.google-oauth2.url=your_backend_url
app.frontend.url=your_frontend_url
```
