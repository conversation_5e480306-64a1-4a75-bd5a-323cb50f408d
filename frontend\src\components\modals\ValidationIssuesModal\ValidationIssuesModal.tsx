import React from 'react';
import { SubmoduleValidationIssue } from '../../../types/project.types';
import './ValidationIssuesModal.css';

interface ValidationIssuesModalProps {
  isOpen: boolean;
  onClose: () => void;
  validationIssues: SubmoduleValidationIssue[];
  userGuidanceMessage?: string;
  title?: string;
}

export const ValidationIssuesModal: React.FC<ValidationIssuesModalProps> = ({
  isOpen,
  onClose,
  validationIssues,
  userGuidanceMessage,
  title = 'Validation Issues Found',
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="validation-issues-modal">
        <div className="modal-header">
          <h2>{title}</h2>
        </div>
        <div className="validation-issues-content">
          {userGuidanceMessage && (
            <div className="guidance-message">
              <p>{userGuidanceMessage}</p>
            </div>
          )}
          
          {validationIssues.length > 0 && (
            <div className="validation-issues-list">
              <h3>Issues Found:</h3>
              <div className="issues-container">
                {validationIssues.map((issue, index) => (
                  <div key={index} className="validation-issue-item">
                    <div className="issue-header">
                      <span className="issue-type">{issue.issueType}</span>
                      <span className="cell-reference">{issue.cellReference}</span>
                    </div>
                    <div className="issue-details">
                      <div className="issue-location">
                        <strong>Sheet:</strong> {issue.sheetName} | 
                        <strong> Column:</strong> {issue.columnLetter} | 
                        <strong> Row:</strong> {issue.rowNumber}
                      </div>
                      <div className="issue-description">
                        {issue.description}
                      </div>
                      {issue.originalName !== issue.correctedName && (
                        <div className="issue-correction">
                          <span className="original-name">"{issue.originalName}"</span>
                          <span className="arrow"> → </span>
                          <span className="corrected-name">"{issue.correctedName}"</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="modal-actions">
            <button 
              type="button" 
              className="btn btn-primary" 
              onClick={onClose}
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ValidationIssuesModal;
