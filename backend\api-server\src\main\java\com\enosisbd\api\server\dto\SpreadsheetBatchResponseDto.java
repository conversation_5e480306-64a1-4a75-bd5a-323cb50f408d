package com.enosisbd.api.server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for representing the complete batch response from Google Sheets API
 * Contains both sheet metadata and all sheet data in a single response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpreadsheetBatchResponseDto {
    /**
     * The spreadsheet ID
     */
    private String spreadsheetId;
    
    /**
     * List of all sheets with their data
     */
    private List<SheetBatchDataDto> sheets;
    
    /**
     * Total number of sheets in the spreadsheet
     */
    private int totalSheets;
    
    /**
     * Number of sheets with successfully retrieved data
     */
    private int successfulSheets;
    
    /**
     * Whether all sheets were processed successfully
     */
    private boolean allSheetsProcessed;
    
    /**
     * Any general error messages from the batch operation
     */
    private List<String> errorMessages;
    
    /**
     * Check if the batch response has any errors
     */
    public boolean hasErrors() {
        return errorMessages != null && !errorMessages.isEmpty() || 
               sheets != null && sheets.stream().anyMatch(sheet -> !sheet.isDataRetrieved());
    }
}
