/* eslint-disable @typescript-eslint/no-explicit-any */
import { SheetTestCase } from '../types/automate-process.types';

export const extractKeywordsWithSynonyms = (testCases: SheetTestCase[]) => {
  if (!testCases || !Array.isArray(testCases) || testCases.length === 0) {
    return [];
  }
  const keywordFrequency = new Map<string, number>();
  const keywords = new Set<string>();
  const synonymMap = {
    login: [
      'signin',
      'sign in',
      'log in',
      'authenticate',
      'credentials',
      'authentication',
      'access',
    ],
    username: ['email', 'user', 'account', 'id', 'userid', 'identifier', 'login name'],
    password: ['pass', 'secret', 'credential', 'passphrase', 'security code', 'pin'],
    submit: ['login', 'enter', 'send', 'continue', 'proceed', 'go', 'confirm', 'apply'],
    logout: ['signout', 'sign out', 'log out', 'exit', 'disconnect', 'end session'],
    register: ['signup', 'sign up', 'create account', 'join', 'enroll', 'new account'],
    click: ['select', 'choose', 'press', 'tap', 'activate', 'trigger'],
    navigate: ['go to', 'visit', 'open', 'browse', 'access', 'load', 'view'],
    back: ['return', 'previous', 'go back', 'backward'],
    forward: ['next', 'advance', 'proceed', 'continue'],
    refresh: ['reload', 'update', 'renew'],
    input: ['enter', 'type', 'fill', 'provide', 'supply', 'insert'],
    select: ['choose', 'pick', 'opt', 'set', 'specify'],
    checkbox: ['check', 'tick', 'mark', 'toggle'],
    radio: ['option', 'select', 'toggle'],
    upload: ['attach', 'upload file', 'browse', 'select file'],
    verify: ['check', 'confirm', 'validate', 'ensure', 'assert', 'test'],
    visible: ['displayed', 'shown', 'present', 'appears', 'viewable', 'rendered'],
    error: ['warning', 'alert', 'notification', 'message', 'invalid'],
    success: ['valid', 'complete', 'confirmed', 'accepted', 'approved'],
    button: ['btn', 'link', 'cta', 'control', 'action'],
    menu: ['dropdown', 'navigation', 'nav', 'options', 'items'],
    search: ['find', 'lookup', 'query', 'filter', 'seek'],
    profile: ['account', 'user profile', 'settings', 'preferences'],
    dashboard: ['home', 'main', 'overview', 'summary', 'control panel'],
    sidebar: ['panel', 'side menu', 'navigation bar', 'side panel'],
    modal: ['dialog', 'popup', 'overlay', 'lightbox', 'window'],
    notification: ['alert', 'message', 'toast', 'banner', 'announcement'],
    save: ['store', 'update', 'commit', 'apply', 'preserve'],
    delete: ['remove', 'clear', 'erase', 'trash', 'discard'],
    edit: ['modify', 'change', 'update', 'alter', 'adjust'],
    cancel: ['abort', 'dismiss', 'close', 'exit', 'discard'],
    payment: ['checkout', 'pay', 'purchase', 'buy', 'transaction'],
    cart: ['basket', 'shopping cart', 'order', 'items'],
    price: ['cost', 'amount', 'fee', 'charge', 'total'],
    checkout: ['payment', 'finish order', 'complete purchase', 'proceed to payment'],
  };

  testCases.forEach((tc) => {
    const extractWords = (text: string | undefined) => {
      if (!text) return;

      // Extract meaningful words (longer than 3 chars)
      const words = text
        .toLowerCase()
        .trim()
        .split(/\s+/)
        .filter((word) => {
          // Filter out common stop words and short words
          const stopWords = new Set([
            'the',
            'and',
            'for',
            'with',
            'that',
            'this',
            'then',
            'from',
            'will',
            'should',
            'have',
            'has',
            'can',
            'able',
            'about',
            'when',
            'where',
            'which',
            'what',
            'who',
            'how',
            'why',
            'not',
            'all',
            'any',
            'are',
            'was',
            'were',
            'been',
            'being',
            'into',
            'onto',
            'upon',
            'after',
            'before',
            'during',
            'under',
            'over',
            'through',
          ]);

          const actionWords = new Set(['add', 'buy', 'pay', 'see', 'set', 'get', 'put', 'use']);

          return (word.length > 3 && !stopWords.has(word)) || actionWords.has(word);
        });

      words.forEach((word) => {
        keywords.add(word);
        keywordFrequency.set(word, (keywordFrequency.get(word) || 0) + 1);
      });

      // Extract key phrases (2-3 word combinations that might be important)
      const wordArray = text.toLowerCase().trim().split(/\s+/);
      if (wordArray.length >= 2) {
        // Extract 2-word phrases
        for (let i = 0; i < wordArray.length - 1; i++) {
          if (wordArray[i].length > 3 && wordArray[i + 1].length > 3) {
            const phrase = `${wordArray[i]} ${wordArray[i + 1]}`;
            if (
              // Check if phrase contains important action words
              phrase.includes('log in') ||
              phrase.includes('sign in') ||
              phrase.includes('sign up') ||
              phrase.includes('check out') ||
              phrase.includes('add to') ||
              phrase.includes('click on') ||
              phrase.includes('enter') ||
              phrase.includes('verify')
            ) {
              keywords.add(phrase);
              keywordFrequency.set(phrase, (keywordFrequency.get(phrase) || 0) + 2); // Higher weight for phrases
            }
          }
        }
      }
    };

    extractWords(tc.description);
    /// commenting this out for now as steps are not being used in the testcases
    // if (tc.steps && Array.isArray(tc.steps)) {
    //   tc.steps.forEach(extractWords);
    // }

    extractWords(tc.expectedResult);
  });

  const extractAcronyms = (phrase: string): string | null => {
    if (!phrase || phrase.length < 5) return null;

    const words = phrase.split(/\s+/);
    if (words.length < 2) return null;

    return words
      .map((w) => w.charAt(0))
      .join('')
      .toLowerCase();
  };

  const baseKeywords = Array.from(keywords);

  baseKeywords.forEach((keyword) => {
    for (const [key, synonyms] of Object.entries(synonymMap)) {
      if (keyword.includes(key) || synonyms.some((syn) => keyword.includes(syn))) {
        keywords.add(key);
        keywordFrequency.set(key, (keywordFrequency.get(key) || 0) + 2);

        synonyms.forEach((syn) => {
          keywords.add(syn);
          keywordFrequency.set(syn, (keywordFrequency.get(syn) || 0) + 1);
        });
      }
    }

    // Process multi-word phrases for acronyms
    if (keyword.includes(' ')) {
      const acronym = extractAcronyms(keyword);
      if (acronym && acronym.length >= 2) {
        keywords.add(acronym);
      }
    }
  });

  // Filter out low-frequency keywords and limit the total number
  const MIN_FREQUENCY = 1;
  const MAX_KEYWORDS = 100;

  return Array.from(keywords)
    .filter((keyword) => (keywordFrequency.get(keyword) || 0) > MIN_FREQUENCY)
    .sort((a, b) => (keywordFrequency.get(b) || 0) - (keywordFrequency.get(a) || 0))
    .slice(0, MAX_KEYWORDS);
};

// Memoization cache for Levenshtein calculations with max size to prevent memory leaks
const levenshteinCache = new Map<string, number>();
const MAX_CACHE_SIZE = 10000;

export const fuzzyMatch = (text: string | undefined, pattern: string | undefined): boolean => {
  if (!text || !pattern) return false;

  const normalizedText = text.toLowerCase().trim();
  const normalizedPattern = pattern.toLowerCase().trim();

  if (normalizedText === normalizedPattern) return true;
  if (normalizedText.includes(normalizedPattern)) return true;
  if (normalizedPattern.length <= 3) return normalizedText.includes(normalizedPattern);
  if (normalizedText.startsWith(normalizedPattern)) return true;

  if (normalizedPattern.length < 3 && normalizedText.length > 20) return false;

  if (normalizedPattern.length >= 2 && normalizedPattern.length <= 5) {
    const words = normalizedText.split(/\s+/);
    if (words.length >= 2) {
      const acronym = words.map((w) => w.charAt(0)).join('');
      if (acronym === normalizedPattern) return true;
    }
  }

  try {
    const threshold = 0.8;
    const allowTranspositions = true;

    // Levenshtein distance calculation with caching
    const calculateLevenshtein = (a: string, b: string): number => {
      // Generate consistent cache key that works regardless of parameter order
      const cacheKey = a.length <= b.length ? `${a}:${b}` : `${b}:${a}`;

      if (levenshteinCache.has(cacheKey)) {
        return levenshteinCache.get(cacheKey)!;
      }

      if (levenshteinCache.size > MAX_CACHE_SIZE) {
        // Clear 20% of the cache when it gets too large
        const keysToDelete = Array.from(levenshteinCache.keys()).slice(0, MAX_CACHE_SIZE / 5);
        keysToDelete.forEach((k) => levenshteinCache.delete(k));
      }

      // Early exit for length difference
      const lenDiff = Math.abs(a.length - b.length);
      if (lenDiff / Math.max(a.length, b.length) > 1 - threshold) {
        return Infinity;
      }

      // Two-row approach for memory efficiency
      let prev = Array(b.length + 1)
        .fill(0)
        .map((_, i) => i);
      let curr = Array(b.length + 1).fill(0);

      for (let i = 1; i <= a.length; i++) {
        curr[0] = i;

        for (let j = 1; j <= b.length; j++) {
          const cost = a[i - 1] === b[j - 1] ? 0 : 1;

          let minCost = Math.min(
            prev[j] + 1, // deletion
            curr[j - 1] + 1, // insertion
            prev[j - 1] + cost, // substitution
          );

          // Add transposition check if enabled
          if (
            allowTranspositions &&
            i > 1 &&
            j > 1 &&
            a[i - 1] === b[j - 2] &&
            a[i - 2] === b[j - 1]
          ) {
            minCost = Math.min(minCost, prev[j - 2] + 1);
          }

          curr[j] = minCost;
        }

        [prev, curr] = [curr, prev];
      }

      const result = prev[b.length];
      levenshteinCache.set(cacheKey, result);
      return result;
    };

    // For longer text, check if pattern appears with some edits allowed
    const words = normalizedText.split(/\s+/);
    for (const word of words) {
      if (word.length < 3) continue;

      const distance = calculateLevenshtein(word, normalizedPattern);
      const maxLength = Math.max(word.length, normalizedPattern.length);
      const similarity = (maxLength - distance) / maxLength;

      if (similarity >= threshold) return true;
    }

    // Efficient sliding window for long texts
    if (normalizedText.length > normalizedPattern.length * 3) {
      const windowSize = normalizedPattern.length + 2;
      // Use larger step size for very long texts
      const step = normalizedText.length > 1000 ? 4 : 2;

      for (let i = 0; i <= normalizedText.length - windowSize; i += step) {
        const window = normalizedText.substring(i, i + windowSize);
        const distance = calculateLevenshtein(window, normalizedPattern);
        const similarity = (windowSize - distance) / windowSize;
        if (similarity >= threshold) return true;
      }
    }

    // Compound word matching for camelCase/PascalCase
    if (normalizedPattern.length > 5 && /[A-Z]/.test(pattern)) {
      const camelCaseParts = normalizedPattern
        .replace(/([A-Z])/g, ' $1')
        .toLowerCase()
        .trim()
        .split(/\s+/);

      if (camelCaseParts.length > 1) {
        const matchesAllParts = camelCaseParts.every(
          (part) =>
            normalizedText.includes(part) ||
            words.some((word) => calculateLevenshtein(word, part) <= 1),
        );

        if (matchesAllParts) return true;
      }
    }

    return false;
  } catch (error) {
    console.debug('Error in fuzzy matching:', error);
    return normalizedText.includes(normalizedPattern);
  }
};

export const filterRelevantElements = (elements: any[], keywords: string[]) => {
  if (!elements || !Array.isArray(elements) || elements.length === 0) return [];

  // Only select truly interactive elements when no keywords are provided
  if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
    return elements.filter((el) => {
      const isInteractive =
        el.t === 'button' ||
        (el.t === 'a' && el.href) ||
        (el.t === 'input' && el.inputType !== 'hidden') ||
        el.t === 'select' ||
        el.t === 'textarea' ||
        el.role === 'button' ||
        (el.clkbl === true && el.vis !== false);

      return isInteractive;
    });
  }

  const processedKeywords = keywords.map((k) => k.toLowerCase().trim());
  const keywordMap = new Map<string, boolean>();
  processedKeywords.forEach((kw) => keywordMap.set(kw, true));

  // Define core action and form-related keywords for special handling
  const actionKeywords = new Set([
    'submit',
    'login',
    'signin',
    'sign in',
    'log in',
    'continue',
    'next',
    'proceed',
    'save',
    'confirm',
    'apply',
    'update',
    'search',
    'add',
    'create',
    'delete',
    'remove',
    'edit',
    'modify',
  ]);

  const formKeywords = new Set([
    'username',
    'user',
    'email',
    'password',
    'name',
    'address',
    'phone',
    'tel',
    'zip',
    'postal',
    'city',
    'state',
    'country',
    'first name',
    'last name',
    'card',
    'credit',
    'payment',
    'checkout',
  ]);

  // Create a scoring function that prioritizes direct matches over fuzzy matches
  const scoreElement = (el: any): number => {
    let score = 0;

    const elementText = el.tx || '';
    const elementId = el.id || '';
    const elementName = el.n || '';
    const elementClass = Array.isArray(el.cls) ? el.cls.join(' ') : el.cls || '';
    const ariaLabel = (el.aria && el.aria.label) || '';
    const placeholder = el.p || '';
    const role = el.role || '';

    const normalizedText = elementText.toLowerCase().trim();
    const normalizedId = elementId.toLowerCase().trim();
    const normalizedName = elementName.toLowerCase().trim();
    const normalizedClass = elementClass.toLowerCase().trim();
    const normalizedAriaLabel = ariaLabel.toLowerCase().trim();
    const normalizedPlaceholder = placeholder.toLowerCase().trim();
    const normalizedRole = role.toLowerCase().trim();

    if (
      !normalizedText &&
      !normalizedId &&
      !normalizedName &&
      !normalizedPlaceholder &&
      !normalizedAriaLabel &&
      !isInteractiveElement(el)
    ) {
      return 0;
    }

    // Base score for interactive elements (this is important but not dominant)
    if (isInteractiveElement(el)) {
      score += 15;

      // Boost for form elements and action buttons
      if (el.t === 'input' || el.t === 'select' || el.t === 'textarea') {
        score += 5;

        // Extra boost for submit/button inputs
        if (el.t === 'input' && ['submit', 'button'].includes(el.inputType)) {
          score += 5;
        }
      }

      // Buttons and anchors get priority
      if (el.t === 'button' || (el.t === 'a' && el.href) || el.role === 'button') {
        score += 10;
      }
    }

    // Gather all attributes for keyword checking
    const attributes = [
      normalizedText,
      normalizedId,
      normalizedName,
      normalizedClass,
      normalizedAriaLabel,
      normalizedPlaceholder,
      normalizedRole,
    ].filter(Boolean);

    // Direct keyword exact matches (highest priority)
    let hasKeywordMatch = false;
    for (const attr of attributes) {
      // Direct matches get highest score
      for (const keyword of processedKeywords) {
        // Exact match (worth 30 points)
        if (attr === keyword) {
          score += 30;
          hasKeywordMatch = true;
        }
        // Contains match (worth 20 points)
        else if (attr.includes(keyword)) {
          score += 20;
          hasKeywordMatch = true;
        }
        // Word boundary match (worth 15 points)
        else if (new RegExp(`\\b${keyword}\\b`).test(attr)) {
          score += 15;
          hasKeywordMatch = true;
        }
      }

      // Special handling for action elements
      if (isInteractiveElement(el)) {
        for (const action of actionKeywords) {
          if (attr.includes(action)) {
            score += 25; // High priority for action elements
          }
        }

        // Special handling for form fields
        if (el.t === 'input' || el.t === 'select' || el.t === 'textarea') {
          for (const formField of formKeywords) {
            if (attr.includes(formField)) {
              score += 20; // High priority for form fields
            }
          }
        }
      }
    }

    // If no direct match found, try fuzzy matching but with lower score
    if (!hasKeywordMatch) {
      for (const attr of attributes) {
        for (const keyword of processedKeywords) {
          if (fuzzyMatch(attr, keyword)) {
            score += 10;
            hasKeywordMatch = true;
          }
        }
      }
    }

    // If it's not interactive and has no match to keywords, it's not relevant
    if (!hasKeywordMatch && !isInteractiveElement(el)) {
      return 0;
    }

    // Penalize deeply nested divs that are unlikely to be important
    if (el.t === 'div' && !normalizedText && !normalizedId) {
      score *= 0.5;
    }

    // Boost elements with high z-index as they might be modals/overlays
    if (el.z && el.z > 100) {
      score += 10;
    }

    // Boost elements likely in viewport
    if (el.inView) {
      score += 5;
    }

    return score;
  };

  const isInteractiveElement = (el: any): boolean => {
    return (
      el.t === 'button' ||
      (el.t === 'a' && el.href) ||
      (el.t === 'input' && el.inputType !== 'hidden') ||
      el.t === 'select' ||
      el.t === 'textarea' ||
      el.role === 'button' ||
      el.onclick === true ||
      el.clkbl === true
    );
  };

  try {
    const scoredElements = elements.map((el) => ({
      element: el,
      score: scoreElement(el),
    }));

    const MIN_SCORE = 15;
    const filteredElements = scoredElements
      .filter((item) => item.score >= MIN_SCORE)
      .map((item) => item.element);

    // If we have too few elements, include core interactive elements
    if (filteredElements.length < 3) {
      const interactiveElements = elements.filter(isInteractiveElement);
      // Deduplicate if any are already in the filtered list
      const additionalElements = interactiveElements.filter(
        (el) => !filteredElements.some((fel) => fel.x === el.x || (fel.id && fel.id === el.id)),
      );

      // Combine but keep original ordering
      const elementIds = new Set(filteredElements.map((el) => el.x || el.id));
      return elements.filter((el) => {
        const id = el.x || el.id;
        if (elementIds.has(id)) return true;
        if (additionalElements.some((ae) => (ae.x || ae.id) === id)) {
          elementIds.add(id);
          return true;
        }
        return false;
      });
    }

    const MAX_ELEMENTS = 120;

    // Return elements in original DOM order to maintain hierarchy
    const filteredIds = new Set(filteredElements.slice(0, MAX_ELEMENTS).map((el) => el.x || el.id));
    return elements.filter((el) => filteredIds.has(el.x || el.id));
  } catch (e) {
    console.debug('Error filtering elements:', e);
    // Fallback to basic filtering of key interactive elements
    return elements.filter(
      (el) =>
        el.t === 'button' ||
        (el.t === 'a' && el.href) ||
        (el.t === 'input' && el.inputType !== 'hidden') ||
        el.role === 'button',
    );
  }
};

// Helper function to check if domJson has the expected structure
export const hasDomElements = (domJson: unknown): domJson is { el: unknown[] } => {
  return (
    domJson !== null && typeof domJson === 'object' && 'el' in domJson && Array.isArray(domJson.el)
  );
};
