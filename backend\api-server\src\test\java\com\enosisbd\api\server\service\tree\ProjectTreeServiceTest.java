package com.enosisbd.api.server.service.tree;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.dto.TreeNodeDto;
import com.enosisbd.api.server.entity.Module;
import com.enosisbd.api.server.entity.SubModule;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ModuleRepository;
import com.enosisbd.api.server.repository.SubModuleRepository;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import com.enosisbd.api.server.service.tree.impl.ProjectTreeServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProjectTreeServiceTest {

    @Mock
    private ProjectService projectService;

    @Mock
    private ModuleService moduleService;

    @Mock
    private SubModuleService subModuleService;

    @Mock
    private ModuleRepository moduleRepository;

    @Mock
    private SubModuleRepository subModuleRepository;

    @InjectMocks
    private ProjectTreeServiceImpl projectTreeService;

    private ProjectDto projectDto;
    private ModuleDto moduleDto1;
    private ModuleDto moduleDto2;
    private SubModule subModule1;
    private SubModule subModule2;
    private SubModule subModule3;

    @BeforeEach
    void setUp() {
        // Create project
        projectDto = new ProjectDto();
        projectDto.setId(1L);
        projectDto.setName("Test Project");
        projectDto.setCreatedAt(LocalDateTime.now());
        projectDto.setUpdatedAt(LocalDateTime.now());
        
        // Create modules
        moduleDto1 = new ModuleDto();
        moduleDto1.setId(1L);
        moduleDto1.setName("Module 1");
        moduleDto1.setProjectId(projectDto.getId());
        
        moduleDto2 = new ModuleDto();
        moduleDto2.setId(2L);
        moduleDto2.setName("Module 2");
        moduleDto2.setProjectId(projectDto.getId());
        
        // Create submodules
        subModule1 = new SubModule();
        subModule1.setId(1L);
        subModule1.setName("SubModule 1");
        Module module1 = new Module();
        module1.setId(moduleDto1.getId());
        subModule1.setModule(module1);
        
        subModule2 = new SubModule();
        subModule2.setId(2L);
        subModule2.setName("SubModule 2");
        subModule2.setModule(module1);
        
        subModule3 = new SubModule();
        subModule3.setId(3L);
        subModule3.setName("SubModule 3");
        Module module2 = new Module();
        module2.setId(moduleDto2.getId());
        subModule3.setModule(module2);
    }

    @Test
    void getProjectTree_ShouldReturnTreeStructure() {
        // Arrange
        Long projectId = 1L;
        List<ModuleDto> modules = Arrays.asList(moduleDto1, moduleDto2);
        List<Long> moduleIds = Arrays.asList(moduleDto1.getId(), moduleDto2.getId());
        List<SubModule> subModules = Arrays.asList(subModule1, subModule2, subModule3);
        
        when(projectService.existsById(projectId)).thenReturn(true);
        when(projectService.getById(projectId)).thenReturn(Optional.of(projectDto));
        when(moduleService.findByProjectId(projectId)).thenReturn(modules);
        when(subModuleRepository.findByModuleIdInJoined(moduleIds)).thenReturn(subModules);
        
        // Act
        TreeNodeDto result = projectTreeService.getProjectTree(projectId);
        
        // Assert
        assertNotNull(result);
        assertEquals(projectId, result.getId());
        assertEquals("Test Project", result.getName());
        assertEquals(EntityType.PROJECT, result.getType());
        assertEquals(2, result.getChildren().size());
        
        // Verify first module
        TreeNodeDto firstModule = result.getChildren().get(0);
        assertEquals(moduleDto1.getId(), firstModule.getId());
        assertEquals(moduleDto1.getName(), firstModule.getName());
        assertEquals(EntityType.MODULE, firstModule.getType());
        assertEquals(2, firstModule.getChildren().size());
        
        // Verify second module
        TreeNodeDto secondModule = result.getChildren().get(1);
        assertEquals(moduleDto2.getId(), secondModule.getId());
        assertEquals(moduleDto2.getName(), secondModule.getName());
        assertEquals(EntityType.MODULE, secondModule.getType());
        assertEquals(1, secondModule.getChildren().size());
        
        // Verify service calls
        verify(projectService).existsById(projectId);
        verify(projectService).getById(projectId);
        verify(moduleService).findByProjectId(projectId);
        verify(subModuleRepository).findByModuleIdInJoined(moduleIds);
    }

    @Test
    void getProjectTree_ShouldThrowNotFoundExceptionWhenProjectDoesNotExist() {
        // Arrange
        Long projectId = 999L;
        when(projectService.existsById(projectId)).thenReturn(false);
        
        // Act & Assert
        NotFoundRestException exception = assertThrows(
            NotFoundRestException.class,
            () -> projectTreeService.getProjectTree(projectId)
        );
        
        assertEquals("Project not found with ID: " + projectId, exception.getMessage());
        verify(projectService).existsById(projectId);
        verify(projectService, never()).getById(anyLong());
        verify(moduleService, never()).findByProjectId(anyLong());
        verify(subModuleRepository, never()).findByModuleIdInJoined(anyList());
    }

    @Test
    void getProjectTree_ShouldReturnEmptyChildrenWhenNoModules() {
        // Arrange
        Long projectId = 1L;
        
        when(projectService.existsById(projectId)).thenReturn(true);
        when(projectService.getById(projectId)).thenReturn(Optional.of(projectDto));
        when(moduleService.findByProjectId(projectId)).thenReturn(Collections.emptyList());
        
        // Act
        TreeNodeDto result = projectTreeService.getProjectTree(projectId);
        
        // Assert
        assertNotNull(result);
        assertEquals(projectId, result.getId());
        assertEquals("Test Project", result.getName());
        assertEquals(EntityType.PROJECT, result.getType());
        assertTrue(result.getChildren().isEmpty());
        
        verify(projectService).existsById(projectId);
        verify(projectService).getById(projectId);
        verify(moduleService).findByProjectId(projectId);
        verify(subModuleRepository, never()).findByModuleIdInJoined(anyList());
    }
}
