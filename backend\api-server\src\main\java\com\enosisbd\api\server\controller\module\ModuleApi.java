package com.enosisbd.api.server.controller.module;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface ModuleApi {
    @PostMapping
    @Operation(summary = "Create a new module")
    @ApiResponse(responseCode = "201", description = "Module created successfully")
    ResponseEntity<RestResponse<ModuleDto>> add(@Valid @RequestBody ModuleDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get module by ID")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    RestResponse<ModuleDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update module")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    RestResponse<ModuleDto> update(
            @PathVariable Long id,
            @Valid @RequestBody ModuleDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete module")
    @ApiResponse(responseCode = "204", description = "Module deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @GetMapping("/{id}/sub-modules")
    @Operation(summary = "Get submodules for a module")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    RestResponse<List<SubModuleDto>> findByModuleId(@PathVariable Long moduleId);
}
