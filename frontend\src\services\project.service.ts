import { RestResponse } from '../types/common.types';
import { Project, ProjectProcessingResult } from '../types/project.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  PROJECTS: '/api/projects',
  PROJECT: (id: number) => `/api/projects/${id}`,
  REFRESH_GOOGLE_SHEET: (id: number) => `/api/projects/${id}/refresh-google-sheet`,
};

export const ProjectService = {
  /**
   * Get all projects
   */
  getAllProjects: async (): Promise<Project[]> => {
    return (await httpService.get<RestResponse<Project[]>>('common', API_ENDPOINTS.PROJECTS)).data;
  },

  /**
   * Get a single project by ID
   */
  getProjectById: async (id: number): Promise<Project> => {
    return (await httpService.get<RestResponse<Project>>('common', API_ENDPOINTS.PROJECT(id))).data;
  },

  /**
   * Create a new project
   */
  createProject: async (
    project: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>,
  ): Promise<ProjectProcessingResult> => {
    return (
      await httpService.post<RestResponse<ProjectProcessingResult>>(
        'common',
        API_ENDPOINTS.PROJECTS,
        project,
      )
    ).data;
  },

  /**
   * Update an existing project
   */
  updateProject: async (
    id: number,
    project: Partial<Project>,
  ): Promise<ProjectProcessingResult> => {
    return (
      await httpService.put<RestResponse<ProjectProcessingResult>>(
        'common',
        API_ENDPOINTS.PROJECT(id),
        project,
      )
    ).data;
  },

  /**
   * Delete a project
   */
  deleteProject: async (id: number): Promise<void> => {
    return httpService.delete('common', API_ENDPOINTS.PROJECT(id));
  },

  /**
   * Refresh project data from Google Sheet
   */
  refreshFromGoogleSheet: async (id: number): Promise<ProjectProcessingResult> => {
    return (
      await httpService.post<RestResponse<ProjectProcessingResult>>(
        'common',
        API_ENDPOINTS.REFRESH_GOOGLE_SHEET(id),
        {},
      )
    ).data;
  },
};

export default ProjectService;
