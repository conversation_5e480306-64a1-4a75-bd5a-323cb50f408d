export interface Project {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  isDirectlyShared?: boolean;
  googleSheetId?: string;
  googleSheetUrl?: string;
  submoduleColumn?: string;
  submoduleStartRow?: number;
  googleUserEmail?: string;
  caseIdColumn?: string;
  testCaseDescriptionColumn?: string;
  testCaseExpectedResultColumn?: string;
  excludedTabs?: string;
}

export interface ProjectFormData {
  name: string;
  description: string;
  googleSheetUrl?: string;
  submoduleColumn?: string;
  submoduleStartRow?: number;
  caseIdColumn?: string;
  testCaseDescriptionColumn?: string;
  testCaseExpectedResultColumn?: string;
  excludedTabs?: string;
}

/**
 * Represents a validation issue found during submodule processing
 */
export interface SubmoduleValidationIssue {
  originalName: string;
  correctedName: string;
  sheetName: string;
  columnLetter: string;
  rowNumber: number; // 1-based row number for user display
  cellReference: string; // e.g., "A5", "B12"
  issueType: string; // e.g., "NAME_TOO_SHORT", "EMPTY_NAME"
  description: string; // Human-readable description of the issue
}

/**
 * Response structure for project operations that include validation
 */
export interface ProjectProcessingResult {
  project: Project;
  validationIssues: SubmoduleValidationIssue[];
  hasValidationIssues: boolean;
  userGuidanceMessage?: string;
}
