import { ExclamationCircleFilled } from '@ant-design/icons';
import { Modal, Typography } from 'antd';

const { Text } = Typography;

interface ConfirmModalProps {
  title: string;
  content: string;
  okText?: string;
  cancelText?: string;
  onOk: () => Promise<void> | void;
  onCancel?: () => void;
  isLoading?: boolean;
  okButtonProps?: {
    loading?: boolean;
    disabled?: boolean;
    danger?: boolean;
  };
}

interface WarningModalProps {
  title: string;
  content: string;
  okText?: string;
  onOk?: () => Promise<void> | void;
  okButtonProps?: {
    loading?: boolean;
    disabled?: boolean;
  };
}

/**
 * A reusable confirmation modal component using Ant Design's Modal.confirm
 *
 * @param title - The title of the confirmation modal
 * @param content - The content/message to display in the modal
 * @param okText - Text for the confirm button (default: "Yes")
 * @param cancelText - Text for the cancel button (default: "No")
 * @param onOk - Function to call when the user confirms
 * @param onCancel - Function to call when the user cancels
 * @param isLoading - Whether the confirmation operation is in progress
 * @param okButtonProps - Additional props for the OK button
 */
export const showConfirmModal = ({
  title,
  content,
  okText = 'Yes',
  cancelText = 'No',
  onOk,
  onCancel,
  isLoading = false,
  okButtonProps = {},
}: ConfirmModalProps) => {
  return Modal.confirm({
    title: title,
    icon: <ExclamationCircleFilled />,
    content: (
      <div>
        <Text>{content}</Text>
      </div>
    ),
    okText: okText,
    cancelText: cancelText,
    onOk: onOk,
    onCancel: onCancel,
    okButtonProps: {
      loading: isLoading,
      danger: title.toLowerCase().includes('delete'),
      ...okButtonProps,
    },
    className: 'ant-confirm-modal',
    centered: true,
  });
};

// Helper function specifically for delete confirmations
export const showDeleteConfirmModal = ({
  itemName = '',
  itemType = 'item',
  onOk,
  onCancel,
  isLoading = false,
  okButtonProps = {},
}: {
  itemName?: string;
  itemType?: string;
  onOk: () => Promise<void> | void;
  onCancel?: () => void;
  isLoading?: boolean;
  okButtonProps?: {
    loading?: boolean;
    disabled?: boolean;
    danger?: boolean;
  };
}) => {
  return showConfirmModal({
    title: `Delete ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`,
    content: `Are you sure you want to delete ${itemName || `this ${itemType}`}?`,
    okText: isLoading ? 'Deleting...' : 'Delete',
    cancelText: 'Cancel',
    onOk,
    onCancel,
    isLoading,
    okButtonProps: {
      ...okButtonProps,
      danger: true,
    },
  });
};

/**
 * A reusable warning modal component using Ant Design's Modal.warning
 *
 * @param title - The title of the warning modal
 * @param content - The content/message to display in the modal
 * @param okText - Text for the OK button (default: "OK")
 * @param onOk - Function to call when the user clicks OK
 * @param okButtonProps - Additional props for the OK button
 */
export const showWarningModal = ({
  title,
  content,
  okText = 'OK',
  onOk,
  okButtonProps = {},
}: WarningModalProps) => {
  return Modal.warning({
    title: title,
    content: (
      <div>
        <Text>{content}</Text>
      </div>
    ),
    okText: okText,
    onOk: onOk,
    okButtonProps: {
      ...okButtonProps,
    },
    className: 'ant-warning-modal',
    centered: true,
  });
};

export default {
  confirm: showConfirmModal,
  delete: showDeleteConfirmModal,
};
