package com.enosisbd.api.server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Optional;

/**
 * SubModule entity representing a submodule within a module
 */
@Entity
@Table(indexes = {
    @Index(name = "idx_submodule_module", columnList = "module_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SubModule extends BaseEntity {

    @NotNull(message = "SubModule name cannot be null")
    @NotBlank(message = "SubModule name cannot be empty")
    @Size(min = 3, max = 255, message = "SubModule name must be between 3 and 255 characters")
    @Column(nullable = false)
    private String name;

    @NotNull(message = "Module is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "module_id", nullable = false)
    private Module module;

    @Column(name = "start_row")
    private Integer startRow;

    @Column(name = "end_row")
    private Integer endRow;

    @OneToOne(mappedBy = "subModule", cascade = CascadeType.REMOVE, orphanRemoval = true)
    private TestScript testScript;

    public Long getModuleId() {
        return Optional.ofNullable(getModule())
                .map(Module::getId)
                .orElse(null);
    }
}
