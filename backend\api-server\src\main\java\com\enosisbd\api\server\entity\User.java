package com.enosisbd.api.server.entity;

import jakarta.persistence.*;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(
        name = "users",
        indexes = {
                @Index(name = "idx_users_email", columnList = "email"),
                @Index(name = "idx_users_full_name", columnList = "fullName"),
                @Index(name = "idx_users_approved", columnList = "approved")
        }
)
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class User extends BaseEntity {

    @Column(name = "fullName", length = 255, unique = false, nullable = false)
    private String fullName;
    @Column(name = "email", length = 255, unique = true, nullable = false)
    private String email;
    @Column(name = "password", length = 100)
    private String password;
    @Column(name = "approved", length = 10)
    private Boolean approved = false;

    @Column(name = "provider", length = 10)
    private String provider; // "GOOGLE", "LOCAL"

    @Column(name = "provider_id", length = 25)
    private String providerId; // Google user's unique ID

    @Column(name = "picture_url", length = 512)
    private String pictureUrl;

    // OAuth token fields for Google API access
    @Column(name = "google_access_token", length = 2048)
    private String googleAccessToken;

    @Column(name = "google_refresh_token", length = 512)
    private String googleRefreshToken;

    @Column(name = "google_token_expiry")
    private LocalDateTime googleTokenExpiry;

    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.REFRESH})
    @JoinTable(
            name = "users_roles",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private UserPreference preference;

    public Boolean isActive() {
        return approved;
    }

    public String getUsername() {
        return email;
    }
}
