package com.enosisbd.api.server.service.submodule.impl;

import com.enosisbd.api.server.dto.SheetTestCaseDto;
import com.enosisbd.api.server.dto.SheetTestCasesResponseDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.entity.Project;
import com.enosisbd.api.server.entity.SubModule;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ModuleRepository;
import com.enosisbd.api.server.repository.SubModuleRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SubModuleServiceImpl implements SubModuleService {
    private final SubModuleRepository repository;
    private final ModuleRepository moduleRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;
    private final GoogleSheetsService googleSheetsService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<SubModuleDto> findAll() {
        List<SubModule> subModules = repository.findAll();
        return entitySharingService.getAccessibleEntities(subModules, EntityType.SUBMODULE)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<SubModuleDto> getById(Long id) {
        return repository.findByIdJoined(id)
                .map(this::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public SubModuleDto add(SubModuleDto dto) {
        SubModule entity = convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long moduleId = dto.getModuleId();
        if (moduleId != null) {
            // Check if user has access to the module's project
            var module = moduleRepository
                    .findById(moduleId)
                    .orElseThrow(() -> BadRequestRestException.with("Module not found with ID: " + moduleId));

            if (!entitySharingService.hasAccess(module.getProject(), EntityType.PROJECT)) {
                throw new BadRequestRestException("You don't have access to this module's project");
            }

            entity.setModule(module);
        }

        repository.save(entity);
        return convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<SubModuleDto> update(SubModuleDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        SubModule existingEntity = maybe.get();
        SubModule entity = convertToEntity(dto);

        // Check if module ID is valid and matches the existing entity
        Long moduleId = dto.getModuleId();
        if (moduleId != null && !moduleId.equals(existingEntity.getModuleId())) {
            throw new BadRequestRestException("Cannot change the module of a submodule");
        }

        // Set the module from the existing entity
        entity.setModule(existingEntity.getModule());

        repository.save(entity);
        return Optional.of(convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        if (!repository.existsById(id)) return Optional.empty();
        repository.deleteById(id);
        return Optional.of(true);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<SubModuleDto> findByModuleId(Long moduleId) {
        return repository.findByModuleIdJoined(moduleId)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public SheetTestCasesResponseDto generateTestCases(Long submoduleId) throws IOException, GeneralSecurityException {
        // Get the submodule
        SubModule subModule = repository.findByIdJoined(submoduleId)
                .orElseThrow(() -> NotFoundRestException.with("SubModule not found with ID: " + submoduleId));

        // Get the project
        Project project = subModule.getModule().getProject();

        // Validate required fields
        if (!StringUtils.hasText(project.getGoogleSheetUrl())) {
            throw new BadRequestRestException("Project does not have a Google Sheet URL");
        }

        if (!StringUtils.hasText(project.getCaseIdColumn())) {
            throw new BadRequestRestException("Project does not have a Case ID column specified");
        }

        if (!StringUtils.hasText(project.getTestCaseDescriptionColumn())) {
            throw new BadRequestRestException("Project does not have a Test Case Description column specified");
        }

        if (!StringUtils.hasText(project.getTestCaseExpectedResultColumn())) {
            throw new BadRequestRestException("Project does not have a Test Case Expected Result column specified");
        }

        if (subModule.getStartRow() == null || subModule.getEndRow() == null) {
            throw new BadRequestRestException("SubModule does not have start and end rows specified");
        }

        // Extract sheet ID from URL
        String sheetId = googleSheetsService.extractSheetId(project.getGoogleSheetUrl());

        // Get the sheet name (module name)
        String sheetName = subModule.getModule().getName();

        // Get test cases from Google Sheets
        List<SheetTestCaseDto> testCases = googleSheetsService.getTestCases(
                sheetId,
                sheetName,
                subModule.getStartRow(),
                subModule.getEndRow(),
                project.getCaseIdColumn(),
                project.getTestCaseDescriptionColumn(),
                project.getTestCaseExpectedResultColumn(),
                authorizationService.getCurrentUsername()
        );

        // Return the response
        return SheetTestCasesResponseDto.builder()
                .testCases(testCases)
                .build();
    }

    private SubModuleDto convertToDto(SubModule entity) {
        SubModuleDto dto = new SubModuleDto();
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setName(entity.getName());
        dto.setModuleId(entity.getModuleId());
        return dto;
    }

    private SubModule convertToEntity(SubModuleDto dto) {
        SubModule entity = new SubModule();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setStartRow(dto.getStartRow());
        entity.setEndRow(dto.getEndRow());
        return entity;
    }
}
