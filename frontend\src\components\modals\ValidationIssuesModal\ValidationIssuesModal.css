.validation-issues-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 700px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.validation-issues-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.guidance-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.guidance-message p {
  margin: 0;
  color: #389e0d;
  font-size: 14px;
  line-height: 1.5;
}

.validation-issues-list h3 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.issues-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.validation-issue-item {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px;
}

.validation-issue-item:last-child {
  border-bottom: none;
}

.issue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.issue-type {
  background-color: #fff2e8;
  color: #d46b08;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.cell-reference {
  background-color: #f6f6f6;
  color: #595959;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.issue-details {
  font-size: 14px;
}

.issue-location {
  color: #8c8c8c;
  margin-bottom: 6px;
  font-size: 13px;
}

.issue-location strong {
  color: #595959;
  margin-right: 4px;
}

.issue-description {
  color: #262626;
  margin-bottom: 8px;
  line-height: 1.4;
}

.issue-correction {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.original-name {
  background-color: #fff1f0;
  color: #cf1322;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.arrow {
  color: #8c8c8c;
  font-weight: bold;
}

.corrected-name {
  background-color: #f6ffed;
  color: #389e0d;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.modal-actions .btn {
  min-width: 80px;
}

/* Responsive design */
@media (max-width: 768px) {
  .validation-issues-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .validation-issues-content {
    padding: 16px;
  }
  
  .issue-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .issue-correction {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
