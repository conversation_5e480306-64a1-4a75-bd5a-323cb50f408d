package com.enosisbd.api.server.service.tree.impl;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.dto.TreeNodeDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ModuleRepository;
import com.enosisbd.api.server.repository.SubModuleRepository;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import com.enosisbd.api.server.service.tree.ProjectTreeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Implementation of the ProjectTreeService
 */
@Service
@RequiredArgsConstructor
public class ProjectTreeServiceImpl implements ProjectTreeService {

    private final ProjectService projectService;
    private final ModuleService moduleService;
    private final SubModuleService subModuleService;
    private final ModuleRepository moduleRepository;
    private final SubModuleRepository subModuleRepository;

    /**
     * Get a tree structure for a project including its modules and submodules
     * This implementation optimizes database calls by fetching all modules and submodules
     * in bulk rather than making separate calls for each module.
     *
     * @param projectId The ID of the project
     * @return A tree structure representing the project hierarchy
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public TreeNodeDto getProjectTree(Long projectId) {
        // Check if project exists
        if (!projectService.existsById(projectId)) {
            throw NotFoundRestException.with("Project not found with ID: " + projectId);
        }
        
        // Get project details
        ProjectDto project = projectService.getById(projectId)
                .orElseThrow(() -> NotFoundRestException.with("Project not found with ID: " + projectId));
        
        // Create root node for the project
        TreeNodeDto rootNode = TreeNodeDto.builder()
                .id(project.getId())
                .name(project.getName())
                .type(EntityType.PROJECT)
                .build();
        
        // Get all modules for this project in a single query
        List<ModuleDto> modules = moduleService.findByProjectId(projectId);
        
        if (modules.isEmpty()) {
            return rootNode;
        }
        
        // Extract all module IDs
        List<Long> moduleIds = modules.stream()
                .map(ModuleDto::getId)
                .collect(Collectors.toList());
        
        // Get all submodules for all modules in a single query
        List<SubModuleDto> allSubModules = subModuleRepository.findByModuleIdInJoined(moduleIds).stream()
                .map(this::convertToSubModuleDto)
                .collect(Collectors.toList());
        
        // Group submodules by moduleId for efficient lookup
        Map<Long, List<SubModuleDto>> subModulesByModuleId = allSubModules.stream()
                .collect(Collectors.groupingBy(SubModuleDto::getModuleId));
        
        // Build the tree structure
        for (ModuleDto module : modules) {
            TreeNodeDto moduleNode = TreeNodeDto.builder()
                    .id(module.getId())
                    .name(module.getName())
                    .type(EntityType.MODULE)
                    .build();
            
            // Add submodules as children to the module node
            List<SubModuleDto> subModules = subModulesByModuleId.getOrDefault(module.getId(), List.of());
            for (SubModuleDto subModule : subModules) {
                TreeNodeDto subModuleNode = TreeNodeDto.builder()
                        .id(subModule.getId())
                        .name(subModule.getName())
                        .type(EntityType.SUBMODULE)
                        .build();
                
                moduleNode.getChildren().add(subModuleNode);
            }
            
            rootNode.getChildren().add(moduleNode);
        }
        
        return rootNode;
    }
    
    /**
     * Convert a SubModule entity to a SubModuleDto
     * 
     * @param subModule The SubModule entity
     * @return The SubModuleDto
     */
    private SubModuleDto convertToSubModuleDto(com.enosisbd.api.server.entity.SubModule subModule) {
        SubModuleDto dto = new SubModuleDto();
        dto.setId(subModule.getId());
        dto.setName(subModule.getName());
        dto.setModuleId(subModule.getModuleId());
        dto.setCreatedAt(subModule.getCreatedAt());
        dto.setUpdatedAt(subModule.getUpdatedAt());
        dto.setCreatedBy(subModule.getCreatedBy());
        return dto;
    }
}
